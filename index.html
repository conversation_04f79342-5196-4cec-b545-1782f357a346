<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集团管控 AES 加解密系统 - SecureVault by lyg</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #667eea;
            border-radius: 10px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 14px;
        }

        .menu {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 30px;
        }

        .menu-button {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .encrypt-btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }

        .decrypt-btn {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
            color: white;
        }

        .menu-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .operation-panel {
            display: none;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .operation-panel.active {
            display: block;
        }

        .operation-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }

        .input-group textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .input-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .process-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .clear-btn {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .back-btn {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        }

        .result-panel {
            display: none;
            background: white;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .result-panel.success {
            border-color: #28a745;
            background: #f8fff9;
        }

        .result-panel.error {
            border-color: #dc3545;
            background: #fff8f8;
        }

        .result-title {
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .result-title.success {
            color: #28a745;
        }

        .result-title.error {
            color: #dc3545;
        }

        .result-content {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }

        .copy-btn {
            margin-top: 10px;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .copy-btn:hover {
            background: #5a6fd8;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 欢迎界面 -->
        <div class="header">
            <h1>集团管控 AES 加解密系统</h1>
            <div class="subtitle">SecureVault by lyg</div>
        </div>

        <!-- 主菜单 -->
        <div id="mainMenu" class="menu">
            <button class="menu-button encrypt-btn" onclick="showEncryptPanel()">
                🔒 文本加密
            </button>
            <button class="menu-button decrypt-btn" onclick="showDecryptPanel()">
                🔓 文本解密
            </button>
        </div>

        <!-- 加密面板 -->
        <div id="encryptPanel" class="operation-panel">
            <div class="operation-title">🔒 文本加密</div>
            <div class="input-group">
                <label for="plaintext">请输入要加密的文本:</label>
                <textarea id="plaintext" placeholder="在此输入需要加密的文本内容..."></textarea>
            </div>
            <div class="action-buttons">
                <button class="action-btn process-btn" onclick="encryptText()">加密</button>
                <button class="action-btn clear-btn" onclick="clearEncrypt()">清空</button>
                <button class="action-btn back-btn" onclick="showMainMenu()">返回</button>
            </div>
            <div id="encryptResult" class="result-panel">
                <div class="result-title">
                    <span id="encryptResultIcon">✅</span>
                    <span id="encryptResultTitle">加密结果</span>
                </div>
                <div id="encryptResultContent" class="result-content"></div>
                <button class="copy-btn" onclick="copyResult('encryptResultContent')">复制结果</button>
            </div>
        </div>

        <!-- 解密面板 -->
        <div id="decryptPanel" class="operation-panel">
            <div class="operation-title">🔓 文本解密</div>
            <div class="input-group">
                <label for="ciphertext">请输入要解密的文本:</label>
                <textarea id="ciphertext" placeholder="在此输入需要解密的密文内容..."></textarea>
            </div>
            <div class="action-buttons">
                <button class="action-btn process-btn" onclick="decryptText()">解密</button>
                <button class="action-btn clear-btn" onclick="clearDecrypt()">清空</button>
                <button class="action-btn back-btn" onclick="showMainMenu()">返回</button>
            </div>
            <div id="decryptResult" class="result-panel">
                <div class="result-title">
                    <span id="decryptResultIcon">✅</span>
                    <span id="decryptResultTitle">解密结果</span>
                </div>
                <div id="decryptResultContent" class="result-content"></div>
                <button class="copy-btn" onclick="copyResult('decryptResultContent')">复制结果</button>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p>集团管控 AES 加解密系统 - 采用 AES-128-CBC 加密算法</p>
            <p>SecureVault by lyg</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
