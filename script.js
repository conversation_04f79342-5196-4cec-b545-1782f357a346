// 集团管控AES加解密系统 - SecureVault by lyg
// JavaScript实现，与Java版本保持兼容

// 密钥字符串（与Java版本保持一致）
const KEY_STRING = "BUYHg67f@WDC1qfv4raz#ESXm89nPOKL";

/**
 * 从种子字符串生成AES密钥
 * 使用与Java版本兼容的方法
 */
function generateKey() {
    // 使用SHA256哈希种子字符串，然后截取前128位作为密钥
    const hash = CryptoJS.SHA256(KEY_STRING);
    return CryptoJS.lib.WordArray.create(hash.words.slice(0, 4)); // 128位 = 4个32位字
}

/**
 * 加密文本
 * @param {string} plaintext - 要加密的明文
 * @returns {string} Base64编码的加密结果
 */
function encrypt(plaintext) {
    try {
        if (!plaintext || plaintext.trim() === '') {
            throw new Error('输入不能为空');
        }

        // 生成密钥
        const key = generateKey();
        
        // 生成随机初始化向量（16字节）
        const iv = CryptoJS.lib.WordArray.random(16);
        
        // 使用AES-CBC模式加密
        const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });
        
        // 将IV和加密数据合并
        const combined = iv.concat(encrypted.ciphertext);
        
        // 返回Base64编码的结果
        return CryptoJS.enc.Base64.stringify(combined);
    } catch (error) {
        throw new Error('加密失败: ' + error.message);
    }
}

/**
 * 解密文本
 * @param {string} ciphertext - 要解密的Base64编码密文
 * @returns {string} 解密后的明文
 */
function decrypt(ciphertext) {
    try {
        if (!ciphertext || ciphertext.trim() === '') {
            throw new Error('输入不能为空');
        }

        // 生成密钥
        const key = generateKey();
        
        // Base64解码
        const combined = CryptoJS.enc.Base64.parse(ciphertext);
        
        // 分离IV（前16字节）和加密数据
        const iv = CryptoJS.lib.WordArray.create(combined.words.slice(0, 4)); // 16字节 = 4个32位字
        const encryptedData = CryptoJS.lib.WordArray.create(combined.words.slice(4));
        
        // 使用AES-CBC模式解密
        const decrypted = CryptoJS.AES.decrypt(
            CryptoJS.lib.CipherParams.create({
                ciphertext: encryptedData
            }), 
            key, 
            {
                iv: iv,
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            }
        );
        
        // 转换为UTF-8字符串
        const result = decrypted.toString(CryptoJS.enc.Utf8);
        
        if (!result) {
            throw new Error('解密失败，请检查密文格式是否正确');
        }
        
        return result;
    } catch (error) {
        throw new Error('解密失败: ' + error.message);
    }
}

// UI控制函数

/**
 * 显示主菜单
 */
function showMainMenu() {
    document.getElementById('mainMenu').style.display = 'flex';
    document.getElementById('encryptPanel').classList.remove('active');
    document.getElementById('decryptPanel').classList.remove('active');
    
    // 清空结果显示
    hideResult('encryptResult');
    hideResult('decryptResult');
}

/**
 * 显示加密面板
 */
function showEncryptPanel() {
    document.getElementById('mainMenu').style.display = 'none';
    document.getElementById('encryptPanel').classList.add('active');
    document.getElementById('decryptPanel').classList.remove('active');
    
    // 清空输入和结果
    document.getElementById('plaintext').value = '';
    hideResult('encryptResult');
    
    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('plaintext').focus();
    }, 100);
}

/**
 * 显示解密面板
 */
function showDecryptPanel() {
    document.getElementById('mainMenu').style.display = 'none';
    document.getElementById('decryptPanel').classList.add('active');
    document.getElementById('encryptPanel').classList.remove('active');
    
    // 清空输入和结果
    document.getElementById('ciphertext').value = '';
    hideResult('decryptResult');
    
    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('ciphertext').focus();
    }, 100);
}

/**
 * 执行加密操作
 */
function encryptText() {
    const plaintext = document.getElementById('plaintext').value;
    
    try {
        const result = encrypt(plaintext);
        showResult('encryptResult', result, true, '加密成功！');
    } catch (error) {
        showResult('encryptResult', error.message, false, '加密失败');
    }
}

/**
 * 执行解密操作
 */
function decryptText() {
    const ciphertext = document.getElementById('ciphertext').value;
    
    try {
        const result = decrypt(ciphertext);
        showResult('decryptResult', result, true, '解密成功！');
    } catch (error) {
        showResult('decryptResult', error.message, false, '解密失败');
    }
}

/**
 * 清空加密输入
 */
function clearEncrypt() {
    document.getElementById('plaintext').value = '';
    hideResult('encryptResult');
    document.getElementById('plaintext').focus();
}

/**
 * 清空解密输入
 */
function clearDecrypt() {
    document.getElementById('ciphertext').value = '';
    hideResult('decryptResult');
    document.getElementById('ciphertext').focus();
}

/**
 * 显示结果
 */
function showResult(resultId, content, isSuccess, title) {
    const resultPanel = document.getElementById(resultId);
    const resultIcon = document.getElementById(resultId.replace('Result', 'ResultIcon'));
    const resultTitle = document.getElementById(resultId.replace('Result', 'ResultTitle'));
    const resultContent = document.getElementById(resultId.replace('Result', 'ResultContent'));
    
    // 设置图标和标题
    if (isSuccess) {
        resultIcon.textContent = '✅';
        resultTitle.textContent = title;
        resultPanel.className = 'result-panel success';
        resultTitle.className = 'result-title success';
    } else {
        resultIcon.textContent = '❌';
        resultTitle.textContent = title;
        resultPanel.className = 'result-panel error';
        resultTitle.className = 'result-title error';
    }
    
    // 设置内容
    resultContent.textContent = content;
    
    // 显示结果面板
    resultPanel.style.display = 'block';
    
    // 滚动到结果位置
    setTimeout(() => {
        resultPanel.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }, 100);
}

/**
 * 隐藏结果
 */
function hideResult(resultId) {
    document.getElementById(resultId).style.display = 'none';
}

/**
 * 复制结果到剪贴板
 */
function copyResult(contentId) {
    const content = document.getElementById(contentId).textContent;
    
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代API
        navigator.clipboard.writeText(content).then(() => {
            showToast('复制成功！');
        }).catch(() => {
            fallbackCopy(content);
        });
    } else {
        // 降级方案
        fallbackCopy(content);
    }
}

/**
 * 降级复制方案
 */
function fallbackCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showToast('复制成功！');
    } catch (err) {
        showToast('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
}

/**
 * 显示提示消息
 */
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #333;
        color: white;
        padding: 12px 20px;
        border-radius: 5px;
        z-index: 1000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后移除
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // Ctrl+Enter 或 Cmd+Enter 执行操作
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        if (document.getElementById('encryptPanel').classList.contains('active')) {
            encryptText();
        } else if (document.getElementById('decryptPanel').classList.contains('active')) {
            decryptText();
        }
    }
    
    // Escape 返回主菜单
    if (event.key === 'Escape') {
        showMainMenu();
    }
});

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    showMainMenu();
    
    // 添加输入框的回车键支持提示
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('keydown', function(event) {
            if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                event.preventDefault();
                if (this.id === 'plaintext') {
                    encryptText();
                } else if (this.id === 'ciphertext') {
                    decryptText();
                }
            }
        });
    });
});
