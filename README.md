# 集团管控 AES 加解密系统 - Web版

**SecureVault by lyg**

这是原 Java 命令行版本的网页实现，保持了所有原有功能和加密兼容性。

## 功能特性

- 🔒 **文本加密**: 使用 AES-128-CBC 算法对文本进行加密
- 🔓 **文本解密**: 解密由本系统加密的密文
- 🔄 **跨平台兼容**: 与原 Java 版本完全兼容
- 📱 **响应式设计**: 支持桌面和移动设备
- 🎨 **现代化界面**: 美观易用的用户界面
- ⌨️ **快捷键支持**: 支持键盘快捷操作

## 技术规格

- **加密算法**: AES-128-CBC
- **密钥生成**: 基于固定种子的确定性密钥生成
- **初始化向量**: 每次加密使用随机 IV
- **编码格式**: Base64 编码输出
- **字符编码**: UTF-8

## 使用方法

### 在线使用
1. 直接在浏览器中打开 `index.html` 文件
2. 选择"文本加密"或"文本解密"功能
3. 输入相应的文本内容
4. 点击对应的操作按钮
5. 复制结果或继续其他操作

### 快捷键
- `Ctrl+Enter` (Windows) 或 `Cmd+Enter` (Mac): 执行当前操作
- `Escape`: 返回主菜单

## 文件结构

```
├── index.html          # 主页面文件
├── script.js           # JavaScript 核心逻辑
└── README.md          # 说明文档
```

## 兼容性说明

本网页版本与原 Java 版本完全兼容：
- 使用相同的密钥生成算法
- 使用相同的加密参数 (AES-128-CBC)
- 使用相同的数据格式 (IV + 密文的 Base64 编码)

这意味着：
- Java 版本加密的数据可以在网页版本中解密
- 网页版本加密的数据可以在 Java 版本中解密

## 安全注意事项

1. **密钥安全**: 系统使用固定的种子字符串生成密钥，适用于内部系统使用
2. **传输安全**: 建议在 HTTPS 环境下使用
3. **数据处理**: 所有加密解密操作都在客户端完成，不会向服务器发送敏感数据

## 浏览器支持

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 依赖项

- [CryptoJS 4.1.1](https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js) - 加密算法库

## 更新日志

### v1.0.0
- 完整实现 Java 版本的所有功能
- 现代化的响应式网页界面
- 跨平台兼容性
- 快捷键支持
- 复制功能

---

**开发者**: lyg  
**版本**: 1.0.0  
**最后更新**: 2025-08-06
