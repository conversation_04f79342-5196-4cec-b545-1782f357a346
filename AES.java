import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.Scanner;

/**
 * 集团管控AES加解密系统 - SecureVault by lyg
 * 提供企业级文本加密和解密功能
 * 采用AES-128-CBC加密算法，确保数据安全
 */
public class AES {
    // 密钥字符串（用于生成AES密钥的种子）
    private static final String KEY_STRING = "BUYHg67f@WDC1qfv4raz#ESXm89nPOKL";
    // 加密算法：AES/CBC/PKCS5Padding
    private static final String ENCRYPTION_ALGORITHM = "AES/CBC/PKCS5Padding";

    public static void main(String[] args) {
        Scanner inputScanner = new Scanner(System.in);

        // 显示欢迎界面
        displayWelcome();

        while (true) {
            displayMainMenu();
            System.out.print("请输入选择 (1-3): ");

            int choice;
            try {
                choice = inputScanner.nextInt();
                inputScanner.nextLine(); // 清空输入缓冲区
            } catch (Exception e) {
                System.out.println("输入无效，请重试。");
                inputScanner.nextLine(); // 清空输入缓冲区
                continue;
            }

            if (choice == 3) {
                System.out.println();
                System.out.println("╭━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╮");
                System.out.println("┃                                                                              ┃");
                System.out.println("┃                        感谢使用 SecureVault 系统                            ┃");
                System.out.println("┃                                                                              ┃");
                System.out.println("┃                                                                              ┃");
                System.out.println("┃                              by lyg                                          ┃");
                System.out.println("┃                                                                              ┃");
                System.out.println("╰━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╯");
                System.out.println();
                break;
            }

            try {
                switch (choice) {
                    case 1:
                        System.out.println("┌──────────────────────────────────────────────────────────────────────────────┐");
                        System.out.println("│                                文本加密                                      │");
                        System.out.println("└──────────────────────────────────────────────────────────────────────────────┘");
                        System.out.print("请输入要加密的文本: ");
                        String plaintext = inputScanner.nextLine();
                        if (plaintext.trim().isEmpty()) {
                            System.out.println("❌ 输入不能为空！");
                            break;
                        }
                        String encryptedResult = encrypt(plaintext);
                        System.out.println();
                        System.out.println("✅ 加密成功！");
                        System.out.println("┌──────────────────────────────────────────────────────────────────────────────┐");
                        System.out.println("│                                加密结果                                      │");
                        System.out.println("├──────────────────────────────────────────────────────────────────────────────┤");
                        System.out.println("│ " + encryptedResult);
                        System.out.println("└──────────────────────────────────────────────────────────────────────────────┘");
                        break;
                    case 2:
                        System.out.println("┌──────────────────────────────────────────────────────────────────────────────┐");
                        System.out.println("│                                文本解密                                      │");
                        System.out.println("└──────────────────────────────────────────────────────────────────────────────┘");
                        System.out.print("请输入要解密的文本: ");
                        String ciphertext = inputScanner.nextLine();
                        if (ciphertext.trim().isEmpty()) {
                            System.out.println("❌ 输入不能为空！");
                            break;
                        }
                        String decryptedResult = decrypt(ciphertext);
                        System.out.println();
                        System.out.println("✅ 解密成功！");
                        System.out.println("┌──────────────────────────────────────────────────────────────────────────────┐");
                        System.out.println("│                                解密结果                                      │");
                        System.out.println("├──────────────────────────────────────────────────────────────────────────────┤");
                        System.out.println("│ " + decryptedResult);
                        System.out.println("└──────────────────────────────────────────────────────────────────────────────┘");
                        break;
                    default:
                        System.out.println("❌ 选项无效，请重试。");
                        break;
                }
            } catch (Exception e) {
                System.out.println();
                System.out.println("❌ 操作失败: " + e.getMessage());
                System.out.println("请检查输入格式是否正确。");
            }
        }

        inputScanner.close();
    }

    /**
     * 生成密钥
     * 使用SHA1PRNG算法从种子字符串生成AES密钥
     */
    private static SecretKey generateKey() throws Exception {
        // 使用SHA1PRNG算法以匹配JavaScript实现
        KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(KEY_STRING.getBytes(StandardCharsets.UTF_8));
        keyGenerator.init(128, secureRandom);
        return keyGenerator.generateKey();
    }

    /**
     * 加密方法
     * @param plaintext 要加密的文本
     * @return Base64编码的加密结果
     */
    private static String encrypt(String plaintext) throws Exception {
        // 从种子生成密钥
        SecretKey key = generateKey();

        // 生成随机初始化向量（16字节）
        byte[] initializationVector = new byte[16];
        SecureRandom randomGenerator = new SecureRandom();
        randomGenerator.nextBytes(initializationVector);

        // 初始化加密器
        Cipher cipher = Cipher.getInstance(ENCRYPTION_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(initializationVector));

        // 加密明文
        byte[] encryptedData = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

        // 合并初始化向量和加密数据
        byte[] ivAndEncryptedData = new byte[initializationVector.length + encryptedData.length];
        System.arraycopy(initializationVector, 0, ivAndEncryptedData, 0, initializationVector.length);
        System.arraycopy(encryptedData, 0, ivAndEncryptedData, initializationVector.length, encryptedData.length);

        // 返回Base64编码的结果
        return Base64.getEncoder().encodeToString(ivAndEncryptedData);
    }

    /**
     * 解密方法
     * @param ciphertext 要解密的Base64编码文本
     * @return 解密后的明文
     */
    private static String decrypt(String ciphertext) throws Exception {
        // 从种子生成密钥
        SecretKey key = generateKey();

        // Base64解码
        byte[] ivAndEncryptedData = Base64.getDecoder().decode(ciphertext);
        // 分离初始化向量（前16字节）和加密数据
        byte[] initializationVector = new byte[16];
        byte[] encryptedData = new byte[ivAndEncryptedData.length - 16];
        System.arraycopy(ivAndEncryptedData, 0, initializationVector, 0, 16);
        System.arraycopy(ivAndEncryptedData, 16, encryptedData, 0, encryptedData.length);
        // 初始化解密器并解密
        Cipher cipher = Cipher.getInstance(ENCRYPTION_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key, new IvParameterSpec(initializationVector));
        byte[] decryptedData = cipher.doFinal(encryptedData);

        return new String(decryptedData, StandardCharsets.UTF_8);
    }

    /**
     * 显示欢迎界面
     */
    private static void displayWelcome() {
        System.out.println();
        System.out.println("╭━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╮");
        System.out.println("┃                                                                              ┃");
        System.out.println("┃                           集团管控 AES 加解密系统                            ┃");
        System.out.println("┃                                                                              ┃");
        System.out.println("┃                                  by lyg                                      ┃");
        System.out.println("┃                                                                              ┃");
        System.out.println("╰━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╯");
        System.out.println();
    }

    /**
     * 显示主菜单
     */
    private static void displayMainMenu() {
        System.out.println("┌──────────────────────────────────────────────────────────────────────────────┐");
        System.out.println("│                                  主菜单                                      │");
        System.out.println("├──────────────────────────────────────────────────────────────────────────────┤");
        System.out.println("│                                                                              │");
        System.out.println("│                           1. 🔒 文本加密                                     │");
        System.out.println("│                                                                              │");
        System.out.println("│                           2. 🔓 文本解密                                     │");
        System.out.println("│                                                                              │");
        System.out.println("│                           3. 🚪 退出系统                                     │");
        System.out.println("│                                                                              │");
        System.out.println("└──────────────────────────────────────────────────────────────────────────────┘");
        System.out.println();
    }
}